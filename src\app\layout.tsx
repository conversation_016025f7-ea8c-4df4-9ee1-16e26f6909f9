import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ModalProvider } from "@/components/providers/ModalProvider";
import { GoogleTagManager } from "@next/third-parties/google";
import { cn } from "@/lib/utils";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Parhlai",
  description: "Accelerate your Entry-Test Preparation with Parhlai",
  openGraph: {
    type: "website",
    url: "https://parhlai.com",
    title: "Parhlai",
    description: "Accelerate your Entry-Test Preparation with Parhlai",
    siteName: "Parhlai",
    locale: "en_US.utf-8",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={cn(inter.className, "m-0 h-full")}>
        <>
          <ModalProvider />
          {children}
          <GoogleTagManager gtmId={process.env.GTMID!} />
        </>
      </body>
    </html>
  );
}
