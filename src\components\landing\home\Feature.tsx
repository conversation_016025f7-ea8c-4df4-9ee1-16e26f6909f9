import { Button } from "@/components/ui/button";
import { getRedirectUrl } from "@/lib/utils";
import Link from "next/link";
import React from "react";

const TailorMadeFeatures = ({ env }: { env?: string }) => {
  const features = [
    {
      title: ["Real-Time", "Performance Analytics"],
      description:
        "Get instant feedback with in-depth insights into your strengths and weaknesses to improve faster.",
    },
    {
      title: ["Timed", "Practice Sessions"],
      description:
        "Enhance your time management skills with customizable timed tests, mimicking real exam conditions.",
    },
    {
      title: ["Progress", "Tracking Dashboard"],
      description:
        "Monitor your preparation journey with visual graphs, milestones, and daily learning goals to stay motivated.",
    },
    {
      title: ["Customizable", "Mock Tests"],
      description:
        "Create mock tests tailored to your syllabus, difficulty level, and time preferences for focused preparation.",
    },
  ];

  return (
    <div className="mx-auto flex w-full max-w-xl flex-col items-center space-y-12 px-4 py-16 sm:max-w-6xl sm:px-12 md:px-16">
      <div className="space-y-10 text-center sm:px-24">
        <h2 className="text-3xl font-semibold sm:text-4xl md:text-5xl">
          Tailor-made features
        </h2>
        <p className=" text-sm text-gray-600 sm:text-lg">
          Empowering your entry test preparation with advanced tools customized
          to meet your unique learning needs.
        </p>
      </div>

      <div className="grid w-full grid-cols-1 gap-12 text-center sm:text-left md:grid-cols-2 lg:gap-16">
        {features.map((feature, index) => (
          <article
            key={index}
            className="group flex h-full flex-col rounded-lg p-4 sm:p-8"
          >
            <div className="mb-4 min-h-[4rem] sm:mb-4 sm:min-h-[4rem] ">
              <h3 className="text-2xl font-medium leading-tight sm:mr-16 sm:text-3xl">
                <span className="text-[#5936CD]">{feature.title[0]}</span>{" "}
                {feature.title[1]}
              </h3>
            </div>
            <p className="text-sm font-light leading-relaxed text-gray-600 sm:text-base md:w-11/12">
              {feature.description}
            </p>
          </article>
        ))}
      </div>
      <Link href={getRedirectUrl("register", env)}>
        <Button size={"cta"}>Register Now</Button>
      </Link>
    </div>
  );
};

export default TailorMadeFeatures;
