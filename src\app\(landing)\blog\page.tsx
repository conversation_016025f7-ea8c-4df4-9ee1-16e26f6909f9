import LabelBanner from "@/components/common/LabelBanner";
import BlogContent from "@/components/landing/blog/BlogContent";
import { BlogProvider } from "@/contexts/BlogContext";
import React from "react";

const BlogPage = () => {
  return (
    <BlogProvider>
      <div className="mt-32 flex w-full flex-col items-center justify-center space-y-16 md:mt-36 lg:mt-0 lg:space-y-24">
        <LabelBanner
          label="What's New Today!"
          iconUrl="/assets/icons/document.svg"
        />
        <BlogContent />
      </div>
    </BlogProvider>
  );
};

export default BlogPage;
