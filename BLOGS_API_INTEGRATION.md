# Blogs API Integration

This document explains how to use the integrated blogs API in your Parhlai landing page project.

## Environment Setup

The project uses your existing environment configuration in `src/lib/config.ts` which defaults to `http://localhost:3001/api` for local development.

## Backend Requirements

Your backend should be running on the configured URL (default: `http://localhost:3001`) with the following endpoints available:

- `GET /api/blogs/` - Get all blogs
- `GET /api/blogs/:id` - Get single blog
- `POST /api/blogs/` - Create blog (requires auth)
- `PUT /api/blogs/:id` - Update blog (requires auth)
- `DELETE /api/blogs/:id` - Delete blog (requires auth)
- `POST /api/blogs/upload-url` - Get image upload URL (requires auth)

## API Functions

### Public Functions (No Authentication Required)

#### `getBlogs(params?: BlogsQueryParams)`
Fetches a list of blogs with optional filtering and pagination.

```typescript
import { getBlogs } from "@/lib/api/blogs";

// Get all blogs
const response = await getBlogs();

// Get blogs with filters
const response = await getBlogs({
  page: 1,
  limit: 10,
  category: "technology",
  search: "react",
  sortBy: "publishedAt",
  sortOrder: "desc"
});
```

#### `getSingleBlog(id: string)`
Fetches a single blog by ID.

```typescript
import { getSingleBlog } from "@/lib/api/blogs";

const response = await getSingleBlog("blog-id-123");
```

### Protected Functions (Require Authentication)

#### `createBlog(blogData: CreateBlogData)`
Creates a new blog post.

```typescript
import { createBlog } from "@/lib/api/blogs";

const response = await createBlog({
  title: "My New Blog Post",
  content: "<p>Blog content here...</p>",
  excerpt: "Short description",
  categoryId: "category-id",
  tagIds: ["tag1", "tag2"],
  status: "published"
});
```

#### `updateBlog(id: string, blogData: UpdateBlogData)`
Updates an existing blog post.

```typescript
import { updateBlog } from "@/lib/api/blogs";

const response = await updateBlog("blog-id-123", {
  title: "Updated Title",
  content: "<p>Updated content...</p>"
});
```

#### `deleteBlog(id: string)`
Deletes a blog post.

```typescript
import { deleteBlog } from "@/lib/api/blogs";

const response = await deleteBlog("blog-id-123");
```

#### `getBlogImageUploadURL(fileName: string, fileType: string)`
Gets a pre-signed URL for uploading blog images.

```typescript
import { getBlogImageUploadURL } from "@/lib/api/blogs";

const response = await getBlogImageUploadURL("image.jpg", "image/jpeg");
```

## Existing Components

Your project already has a complete blog system with the following components:

### Existing Blog Structure
- `/blog` - Main blog listing page (`src/app/(landing)/blog/page.tsx`)
- `/blog/[slug]` - Single blog post page (`src/app/(landing)/blog/[slug]/page.tsx`)

### Existing Blog Components
- `BlogContent.tsx` - Main blog content wrapper
- `LatestNews.tsx` - Latest news section with blog cards
- `RecentActivity.tsx` - Recent activity section
- `BlogCard.tsx` - Individual blog card component
- `SingleBlogPost.tsx` - Single blog post component

### Integration Status ✅ COMPLETED
The API has been fully integrated into your existing components:

- **BlogContext** - Centralized state management for blogs
- **LatestNews.tsx** - Now fetches latest blogs from API with fallback to mock data
- **RecentActivity.tsx** - Now fetches recent blogs with sorting functionality
- **SingleBlogPost.tsx** - Now fetches individual blog posts by slug
- **Blog Pages** - Wrapped with BlogProvider for context access

All components include loading states, error handling, and show "No Data" message when no blogs are available from the API.

## Types

The following TypeScript types are available:

- `Blog` - Main blog object
- `BlogsResponse` - Response for multiple blogs
- `SingleBlogResponse` - Response for single blog
- `BlogsQueryParams` - Query parameters for filtering blogs
- `CreateBlogData` - Data for creating a blog
- `UpdateBlogData` - Data for updating a blog
- `BlogImageUploadResponse` - Response for image upload URL

## Usage Examples

### Integrating with Existing LatestNews Component

```tsx
// Update src/components/landing/blog/LatestNews.tsx
"use client";
import React, { useState, useEffect } from "react";
import { getBlogs } from "@/lib/api/blogs";
import BlogCard from "./BlogCard";
import { cn } from "@/lib/utils";

const LatestNews = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLatestPosts = async () => {
      const response = await getBlogs({
        limit: 2,
        sortBy: "publishedAt",
        sortOrder: "desc"
      });

      if (response.success) {
        setPosts(response.blogs);
      }
      setLoading(false);
    };

    fetchLatestPosts();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="w-full">
      <h2 className="mb-5 text-3xl font-bold text-accent lg:text-5xl">
        Latest News
      </h2>
      <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
        {posts.map((post, index) => (
          <div
            key={post.id}
            className={cn(index === 0 ? "md:col-span-3" : "md:col-span-2")}
          >
            <BlogCard
              {...post}
              isLarge={index === 0}
              isMedium={index === 1}
              slug={post.slug}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Integrating with Existing SingleBlogPost Component

```tsx
// Update src/components/landing/blog/SingleBlogPost.tsx
"use client";
import React, { useState, useEffect } from "react";
import { getSingleBlog } from "@/lib/api/blogs";

const SingleBlogPost = ({ slug }: { slug: string }) => {
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPost = async () => {
      // You might need to convert slug to ID or modify your API to accept slugs
      const response = await getSingleBlog(slug);

      if (response.success) {
        setPost(response.blog);
      }
      setLoading(false);
    };

    fetchPost();
  }, [slug]);

  if (loading) return <div>Loading...</div>;
  if (!post) return <div>Blog not found</div>;

  // Rest of your existing component logic...
};
```

### Custom Blog Implementation

```tsx
"use client";
import { useState, useEffect } from "react";
import { getBlogs } from "@/lib/api/blogs";

export default function CustomBlogsList() {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlogs = async () => {
      const response = await getBlogs({ limit: 5, featured: true });
      if (response.success) {
        setBlogs(response.blogs);
      }
      setLoading(false);
    };

    fetchBlogs();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {blogs.map(blog => (
        <div key={blog.id}>
          <h3>{blog.title}</h3>
          <p>{blog.excerpt}</p>
        </div>
      ))}
    </div>
  );
}
```

## Error Handling

All API functions return a consistent response format with error handling:

```typescript
const response = await getBlogs();

if (response.success) {
  // Handle success
  console.log(response.blogs);
} else {
  // Handle error
  console.error(response.message);
}
```

## Next Steps

1. Start your backend server on the configured port
2. Import and use the blog components in your pages
3. Customize the styling to match your design system
4. Add authentication for protected routes if needed
5. Implement additional features like search, filtering, etc.
