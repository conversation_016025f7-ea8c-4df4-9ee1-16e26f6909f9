"use client";
import { But<PERSON> } from "@/components/ui/button";
import { getRedirectUrl } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { Cursor, useTypewriter } from "react-simple-typewriter";

const Description = ({ env }: { env?: string }) => {
  const [text] = useTypewriter({
    words: [
      "NET Preparation",
      "MDCAT Preparation",
      "ECAT Preparation",
      "NAT Preparation",
      "NTS Preparation",
      "NUMS Preparation",
    ],
    loop: false,
    typeSpeed: 50,
    deleteSpeed: 40,
    delaySpeed: 1300,
  });

  return (
    <div className="flex w-full flex-col items-center space-y-4 sm:space-y-8">
      <div className="h-10 sm:h-16">
        <Image
          src="/assets/icons/parhlai_expanded.svg"
          alt="icon"
          width={0}
          height={0}
          style={{ objectFit: "contain", width: "100%", height: "100%" }}
        />
      </div>

      <h2 className="bg-gradient-to-r from-primary to-[#8A7FB0] bg-clip-text text-center text-2xl font-semibold uppercase tracking-[0.4em] text-transparent sm:tracking-[0.6em] sm:max-md:text-3xl md:text-4xl">
        coming soon
      </h2>
      <h1
        className="bg-gradient-to-r from-primary to-[#DED7F5] bg-clip-text text-center text-[34px] font-extrabold
        text-transparent sm:max-md:text-5xl md:text-7xl"
      >
        AI-Assisted{"  "}
        <br className="xl:hidden" />
        <span>{text}</span>
        <Cursor cursorColor="#5936CD" cursorBlinking />{" "}
      </h1>
      <h3 className="max-w-[100%] px-[5%] text-center text-base font-light sm:text-[18px] md:text-2xl xl:px-[17%]">
        Parhlai is your AI-guided solution for mastering university entry tests
        in Pakistan. Prepare with confidence, ensuring your success with our
        cutting-edge platform tailored to your needs.
      </h3>
      <Link href={getRedirectUrl("register", env)}>
        <Button size={"cta"}>Register Now</Button>
      </Link>
      <div>
        <div className="mt-16 h-[238px] w-[334px] sm:h-[428px] sm:w-[600px] lg:h-[660px] lg:w-[928px]">
          <Image
            src="/assets/images/dashboard.png"
            alt="Dashboard"
            width={0}
            height={0}
            sizes="210rem"
            style={{ objectFit: "contain", width: "100%", height: "100%" }}
            priority // Keep priority - this is the hero image above the fold
          />
        </div>
      </div>
    </div>
  );
};

export default Description;
