"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetClose, SheetTrigger } from "../../ui/sheet";
import { Button } from "../../ui/button";
import { Cross1Icon, HamburgerMenuIcon } from "@radix-ui/react-icons";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { getRedirectUrl } from "@/lib/utils";

const NavigationSheet = ({ env }: { env?: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const navItems = [
    { label: "Home", href: "/", type: "link" },
    {
      label: "Testimonials",
      href: pathname === "/" ? "#testimonial" : "/#testimonial",
      type: "scroll",
    },
    {
      label: "Product",
      href: pathname === "/" ? "#learning-revolution" : "/#learning-revolution",
      type: "scroll",
    },
    // {
    //   label: "Pricing",
    //   href: pathname === "/" ? "#learning-revolution" : "/#learning-revolution",
    //   type: "scroll",
    // },
    { label: "Blog", href: "/blog", type: "link" },
    { label: "Contact us", href: "/contact", type: "link" },
  ];

  // Helper function to perform smooth scroll
  const smoothScrollToElement = (targetId: string) => {
    const element = document.getElementById(targetId);
    if (element) {
      // Calculate the offset to account for navigation height
      // Mobile nav is h-20 (80px) + some buffer for proper spacing
      const navHeight = 80; // Mobile nav height
      const elementPosition =
        element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navHeight;

      // Scroll to the calculated position
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  // Helper function to extract ID from href
  const extractTargetId = (href: string): string => {
    return href.startsWith("/#") ? href.slice(2) : href.slice(1);
  };

  // Navigation click handler with smooth scrolling
  const handleNavigation = (item: (typeof navItems)[0]) => {
    if (item.type === "scroll") {
      const targetId = extractTargetId(item.href);

      if (pathname !== "/") {
        // Navigate to home page first, then scroll
        router.push("/");

        // Use a single reliable method for post-navigation scrolling
        let hasScrolled = false;

        const attemptScroll = () => {
          if (hasScrolled) return; // Prevent double scrolling

          const element = document.getElementById(targetId);
          if (element) {
            smoothScrollToElement(targetId);
            hasScrolled = true;
          } else setTimeout(attemptScroll, 50); // Retry if element not found yet (max 10 attempts)
        };

        // Start attempting to scroll after navigation
        setTimeout(attemptScroll, 100);
      } else smoothScrollToElement(targetId);
    } else router.push(item.href);
  };

  return (
    <Sheet onOpenChange={(isOpen) => setIsOpen(isOpen)}>
      {isOpen ? (
        <SheetClose asChild>
          <Button variant="icon" className="px-0 lg:hidden">
            <Cross1Icon width={24} height={24} />
          </Button>
        </SheetClose>
      ) : (
        <SheetTrigger asChild>
          <Button variant="icon" className="px-0 lg:hidden">
            <HamburgerMenuIcon width={24} height={24} />
          </Button>
        </SheetTrigger>
      )}
      <SheetContent
        side="bottom"
        className="flex h-[calc(100%-80px)] flex-col justify-between space-y-8 rounded-t-[32px] border-0 md:h-[calc(100%-96px)] lg:hidden"
      >
        {/* <SheetHeader>
          <div className="flex h-12 justify-between px-[5%]">
            <div className="flex h-full justify-center">
              <Image
                src="/assets/icons/parhlai_white_purple.svg"
                alt="icon"
                width={0}
                height={0}
                style={{ objectFit: "contain", width: "auto", height: "100%" }}
              />
            </div>
            <SheetClose asChild>
              <Button variant="icon">
                <Cross1Icon width={20} height={20} />
              </Button>
            </SheetClose>
          </div>
        </SheetHeader> */}
        <div className="flex w-full flex-col items-center justify-between gap-y-12 px-[5%]">
          {navItems.map((link, index) => (
            <SheetClose key={index} asChild>
              <Button
                variant="link"
                className="h-fit p-0"
                onClick={() => handleNavigation(link)}
              >
                {link.label}
              </Button>
            </SheetClose>
          ))}
        </div>
        <div className="flex w-full flex-col items-center gap-y-4">
          <SheetClose asChild>
            <Link
              className="w-full max-w-[450px]"
              href={getRedirectUrl("register", env)}
            >
              <Button size={"lg"} className="w-full rounded-[10px]">
                Register
              </Button>
            </Link>
          </SheetClose>
          <SheetClose asChild>
            <Link
              className="w-full max-w-[450px]"
              href={getRedirectUrl("login", env)}
            >
              <Button
                variant={"link"}
                size={"lg"}
                className="w-full rounded-[10px]"
              >
                Log In
              </Button>
            </Link>
          </SheetClose>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default NavigationSheet;
