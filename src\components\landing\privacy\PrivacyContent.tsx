import Link from "next/link";
import React from "react";

const PrivacyContent = () => {
  const privacyPolicyContent = [
    {
      heading: "Collection of Data",
      content:
        "Anonymous Information: Our web server automatically recognizes anonymous data, not linked to personal information, such as IP (Internet Protocol) address, browser type, operating system, domain name, access times, and referring website addresses, for each visitor to our website. Personal Data: When you register as a member or request information, we collect personal data through our registration and online enrollment forms. This may include your full name, email address, telephone number, and educational background. Additionally, any personal data voluntarily provided via email or offline communication may be collected. Most public pages on our website can be visited without providing personal data.",
    },
    {
      heading: "Use of Data",
      content:
        "We share personal data with third parties only as described in this privacy policy and do not sell it. The information collected is used for the purpose for which it was provided, to enhance our website content, customize user experience, notify users about updates, and contact users for marketing or employment opportunities. Personal data may also be disclosed when legally required or to protect our rights and the safety of others. Persons providing telephone numbers online may only receive contact related to placed orders, technical support, billing, product-related issues, or Parhlai matters. In addition to anonymous and personal data, we may collect specific information from users on iOS, Android, and iPadOS devices to improve our service. This data is solely for enhancing user experience and will not be shared with third parties unless otherwise stated.",
    },
    {
      heading: "Cookies",
      content:
        "We utilize cookies to store preferences, record session information, and customize webpage content based on user interaction. Cookies are small files stored on a user’s computer, and while most expire within 30-365 days, some may remain longer. Disabling cookies may limit certain features on our site.",
    },
    {
      heading: "3rd Party Tracking",
      content:
        "Our privacy statement does not cover the use of cookies by our partners, affiliates, tracking utility company, or service providers. We do not have access or control over these cookies.",
    },
    {
      heading: "Security",
      content:
        "We employ generally accepted security standards to safeguard personal data during transmission and upon receipt. While no method of transmission over the internet is 100% secure, we strive to protect your information to the best of our ability.",
    },
    {
      heading: "Transmission of Personal Data",
      content:
        "By providing information to  Parhlai through the site, users consent to the transmission of such information internationally, as necessary, for processing in accordance with Parhlai standard business practices.",
    },
    {
      heading: "Review and Correction",
      content:
        "Upon request, users can correct inaccuracies in contact information or delete their account. Information is retained as long as necessary to provide services and comply with legal obligations.",
    },
    {
      heading: "Social Media Widgets",
      content:
        "Our website features social media widgets that may collect IP addresses and set cookies to function properly. Interactions with these features are governed by the privacy policy of the providing company.",
    },
    {
      heading: "Links to 3rd Party Sites",
      content:
        "Links on our website to third-party sites are governed by their respective privacy policies.",
    },
    {
      heading: "Testimonials",
      content:
        "With user consent, we may display testimonials on our site. Users can update or delete their testimonials by contacting us.",
    },
    {
      heading: "Maintaining Privacy",
      content:
        "While we maintain confidentiality of personal communications transmitted directly to Parhlai, information posted in public forums is not confidential. Requests for removal of personal information from public forums can be made.",
    },
    {
      heading: "Changes To This Policy",
      content:
        "This privacy policy may be updated at any time to reflect changes in information practices. Users will be notified of material changes via email or notice on our website.",
    },
  ];

  return (
    <div className="flex h-fit w-[92%] flex-col rounded-[16px] bg-white px-11 py-8 drop-shadow-xl">
      <h3 className=" mb-2 text-3xl font-extrabold">Privacy Policy</h3>
      <p className="font-base mb-10 text-gray-400">
        Last updated 2nd May, 2024
      </p>
      <p className="leading-10">
        <Link
          className="text-base underline"
          target="_blank"
          href={"https://parhlai.com"}
        >
          Parhlai! (“www.Parhlai.com”)
        </Link>{" "}
        values the privacy of individuals who utilize the Parhlai website or any
        other Parhlai services. This privacy policy outlines our current
        information practices. Any future updates to our privacy policy will be
        promptly posted on this page. In this policy, “you” or “your” refers to
        any individual or entity subscribing to and/or utilizing the Service
        (“Users”). Unless explicitly stated otherwise, “Parhlai,” “we,” or “our”
        collectively refer to www.parhlai.com
      </p>
      {privacyPolicyContent.map((section, index) => (
        <div key={index} className="">
          <h4 className="text-lg font-semibold leading-10">
            {section.heading}
          </h4>
          <p className=" leading-10">{section.content}</p>
        </div>
      ))}
      <p>
        For inquiries regarding privacy or this policy, contact us at{" "}
        <span className="text-primary"><EMAIL></span>
      </p>
    </div>
  );
};

export default PrivacyContent;
