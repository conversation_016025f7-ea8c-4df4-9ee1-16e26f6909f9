const NODE_ENV = process.env.NODE_ENV;
const APP_ENV = process.env.APP_ENV;
/** @type {import('next').NextConfig} */

const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'user-profile-pictures-preview.35622a344d033cf59bea40689718cbf4.r2.cloudflarestorage.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: "/register",
        destination:
          process.env.APP_ENV === "production"
            ? "https://app.parhlai.com/account/register"
            : process.env.APP_ENV === "preview"
              ? "https://app-preview.parhlai.com/account/register"
              : "/register",
        permanent: true,
      },
      {
        source: "/login",
        destination:
          process.env.APP_ENV === "production"
            ? "https://app.parhlai.com/account/login"
            : process.env.APP_ENV === "preview"
              ? "https://app-preview.parhlai.com/account/login"
              : "/login",
        permanent: true,
      },
    ];
  },
  output: "standalone",
  reactStrictMode: true,
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  webpack: (config) => {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // We upload source maps for production.
    if (NODE_ENV === "production") {
      // `config.devtool` must be 'hidden-source-map' or 'source-map' to properly pass sourcemaps.
      // https://github.com/vercel/next.js/blob/89ec21ed686dd79a5770b5c669abaff8f55d8fef/packages/next/build/webpack/config/blocks/base.ts#L40
      config.devtool = "hidden-source-map";
    }

    return config;
  },
};

export default nextConfig;
