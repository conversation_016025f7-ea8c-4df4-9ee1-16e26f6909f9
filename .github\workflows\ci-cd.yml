name: Build and Test
on:
  pull_request:
    branches: [main, preview]

jobs:
  Build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.11.1"

      - name: Setup yarn v4
        run: corepack enable && corepack prepare yarn@4.5.1

      - name: Install dependencies
        run: yarn install

      - name: Build
        run: yarn build:cf
