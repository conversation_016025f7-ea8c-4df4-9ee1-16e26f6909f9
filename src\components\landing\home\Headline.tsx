"use client";
import { But<PERSON> } from "@/components/ui/button";
import { getRedirectUrl } from "@/lib/utils";
import Link from "next/link";
import React from "react";

const Headline = ({ env }: { env?: string }) => {
  return (
    <div className="hidden w-full items-center justify-center gap-x-4 bg-primary-200 py-2 font-medium text-white lg:flex">
      ⚡ Get 100% discount by using code “FREE ACCESS”
      <Link href={getRedirectUrl("register", env)}>
        <Button
          variant="link"
          className="py-0 font-semibold text-white underline underline-offset-2"
        >
          Sign Up Now
        </Button>
      </Link>
    </div>
  );
};

export default Headline;
