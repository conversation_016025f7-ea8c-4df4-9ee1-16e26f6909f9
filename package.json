{"name": "frontend-parhlai", "packageManager": "yarn@4.1.1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --write --ignore-path .prettierignore .", "prettier:check": "prettier --check --ignore-path .prettierignore .", "build:cf": "npx @cloudflare/next-on-pages", "run:cf": "npx wrangler pages dev .vercel/output/static --compatibility-flag=nodejs_compat"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@hookform/resolvers": "^3.3.4", "@next/third-parties": "^14.2.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.1.1", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "0.2.1", "embla-carousel-react": "^8.5.2", "lucide-react": "^0.372.0", "next": "^14.2.2", "radix-ui": "^1.0.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.51.2", "react-simple-typewriter": "^5.0.1", "sharp": "^0.33.3", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "autoprefixer": "^10.0.1", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "typescript": "^5.4.5"}}