import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

type FeatureCardProps = {
  title: string;
  description: string;
  imageUrl: string;
  backgroundColor: string;
};

const FeatureCard = ({
  title,
  description,
  imageUrl,
  backgroundColor,
}: FeatureCardProps) => {
  return (
    <div
      className={cn(
        backgroundColor,
        "flex min-w-[45%] flex-1 flex-col items-center space-y-4 rounded-[32px] p-[13px] sm:p-[64px]",
      )}
    >
      <div className="relative h-[100px] w-[106px] sm:h-[160px] sm:w-[210px]">
        <Image
          src={imageUrl}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          alt="robot"
        />
      </div>
      <h3 className="text-center text-2xl font-bold text-white sm:text-3xl lg:whitespace-nowrap">
        {title}
      </h3>
      <p className="text-center text-base font-light text-white">
        {description}
      </p>
    </div>
  );
};

export default FeatureCard;
