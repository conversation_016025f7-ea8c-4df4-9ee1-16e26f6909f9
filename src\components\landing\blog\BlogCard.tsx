import React from "react";
import Image from "next/image";
import Link from "next/link";

type BlogCardProps = {
  id: number;
  title: string;
  excerpt: string;
  image: string;
  tags?: string[];
  isLarge?: boolean;
  isMedium?: boolean;
  isSmallMedium?: boolean;
  slug: string;
};

const BlogCard = ({
  title,
  excerpt,
  image,
  tags = [],
  isLarge = false,
  isMedium = false,
  isSmallMedium = false,
  slug,
}: BlogCardProps) => {
  if (isLarge)
    // Large card layout - same height as medium but wider (spans 2 columns)
    return (
      <Link href={`/blog/${slug}`} className="block h-full">
        <div className="group h-full cursor-pointer overflow-hidden rounded-[10px] bg-[#D9D9D9] shadow-lg transition-shadow duration-300 hover:shadow-xl">
          <div className="relative h-56 overflow-hidden">
            <Image
              src={image}
              alt={title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
            />
          </div>

          <div className="flex flex-col px-6 pb-7 pt-5">
            <h3 className="mb-2 line-clamp-2 text-2xl font-bold text-accent">
              {title}
            </h3>
            <p className="mb-4 line-clamp-4 flex-grow text-base text-black">
              {excerpt}
            </p>

            {/* Tags at bottom if any */}
            {tags.length > 0 && (
              <div className="mt-auto flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="rounded-[20px] bg-accent px-3.5 py-1 text-sm font-medium text-white"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </Link>
    );

  if (isMedium)
    // Medium card layout - slightly bigger than normal
    return (
      <Link href={`/blog/${slug}`} className="block h-full">
        <div className="group h-full cursor-pointer overflow-hidden rounded-[10px] bg-[#D9D9D9] shadow-lg transition-shadow duration-300 hover:shadow-xl">
          <div className="relative h-56 overflow-hidden">
            <Image
              src={image}
              alt={title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>

          <div className="flex flex-col px-6 pb-7 pt-5">
            {/* Tags at top if any */}
            {tags.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="rounded-[20px] bg-accent px-3.5 py-1 text-sm font-medium text-white"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            <h3 className="mb-2 line-clamp-2 text-2xl font-bold text-accent">
              {title}
            </h3>
            <p className="line-clamp-4 flex-grow text-base text-black">
              {excerpt}
            </p>
          </div>
        </div>
      </Link>
    );

  if (isSmallMedium)
    // Small-medium card layout - between normal and medium
    return (
      <Link href={`/blog/${slug}`} className="block h-full">
        <div className="group h-full cursor-pointer overflow-hidden rounded-[10px] bg-[#D9D9D9] shadow-lg transition-shadow duration-300 hover:shadow-xl">
          <div className="relative h-52 overflow-hidden">
            <Image
              src={image}
              alt={title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>

          <div className="flex flex-col px-5 pb-6 pt-4">
            {/* Tags at top if any */}
            {tags.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="rounded-[20px] bg-accent px-3 py-1 text-xs font-medium text-white"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            <h3 className="mb-2 line-clamp-2 text-xl font-bold text-accent">
              {title}
            </h3>
            <p className="line-clamp-3 flex-grow text-sm text-black">
              {excerpt}
            </p>
          </div>
        </div>
      </Link>
    );

  // Normal card layout
  return (
    <Link href={`/blog/${slug}`} className="block h-full">
      <div className="group h-full cursor-pointer overflow-hidden rounded-[10px] bg-[#D9D9D9] shadow-lg transition-shadow duration-300 hover:shadow-xl">
        <div className="relative h-48 overflow-hidden">
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>

        <div className="flex flex-col p-4">
          {/* Tags at top if any */}
          {tags.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="rounded-full bg-accent px-3 py-1 text-sm font-medium text-white"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          <h3 className="mb-3 line-clamp-2 text-lg font-bold text-accent">
            {title}
          </h3>
          <p className="mb-4 line-clamp-3 flex-grow text-sm text-black">
            {excerpt}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
