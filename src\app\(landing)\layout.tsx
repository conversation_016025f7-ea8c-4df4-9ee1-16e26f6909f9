import Headline from "@/components/landing/home/<USER>";
import HomeFooter from "@/components/landing/home/<USER>";
import HomeNav from "@/components/landing/home/<USER>";
import React from "react";

const HomeLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <>
      <Headline env={process.env.APP_ENV} />
      <div className="relative flex h-full flex-col items-center gap-y-16 lg:gap-y-24">
        <HomeNav env={process.env.APP_ENV} />
        {/* Spacer div to prevent content jump when nav becomes sticky on large screens */}
        <div className="hidden lg:h-24" id="nav-spacer" />
        {children}
        <HomeFooter />
      </div>
    </>
  );
};

export default HomeLayout;
