"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { getRedirectUrl } from "@/lib/utils";

const LearningRevolution = ({ env }: { env?: string }) => {
  const features = [
    {
      title: "Unlimited MCQ Generation",
      description:
        "Never run out of challenging questions with our AI-powered Multiple Choice Questions (MCQs).",
      image: "/assets/icons/mcq_robot.png",
      dashboard: "/assets/images/mcq_dashboard.png",
      isReversed: false,
    },
    {
      title: "Insightful AI Analytics",
      description:
        "Get valuable insights into your performance with our AI-fueled analytics.",
      image: "/assets/icons/analytics_robot.png",
      dashboard: "/assets/images/analytics_dashboard.png",
      isReversed: true,
    },
  ];

  return (
    <div
      id="learning-revolution"
      className="w-full bg-gradient-to-b from-primary to-customPurple"
    >
      <div className="mx-auto my-20 flex w-full max-w-7xl flex-col items-center space-y-12 px-4 py-20 sm:px-12 md:my-28 md:px-16">
        <div className="space-y-10 text-center text-white">
          <h2 className="text-3xl font-semibold sm:text-4xl md:text-5xl">
            AI-Powered Learning Revolution
          </h2>
          <p className="max-w-3xl text-sm font-light text-gray-200 sm:text-lg ">
            Experience the future of test preparation with AI-driven MCQ
            generation and in-depth analytics to track and enhance your
            performance effortlessly.
          </p>
        </div>
        <Link href={getRedirectUrl("register", env)}>
          <Button variant={"white"} size={"cta"}>
            Register Now
          </Button>
        </Link>

        <div className="mt-16 w-full md:space-y-32">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`flex flex-col items-center justify-between lg:flex-row lg:items-start ${
                feature.isReversed ? "lg:flex-row-reverse" : ""
              }`}
            >
              <div
                className={`mx-8 mt-12 flex w-full flex-col items-center space-y-6 text-center ${
                  feature.isReversed
                    ? "md:pl-8 lg:items-end lg:text-right"
                    : "md:pr-8 lg:items-start lg:text-left"
                } lg:w-1/3`}
              >
                <div className="mb-4 h-20 w-20 ">
                  <Image
                    src={feature.image}
                    alt={feature.title}
                    width={0}
                    height={0}
                    style={{
                      objectFit: "contain",
                      width: "auto",
                      height: "100%",
                    }}
                    loading="lazy"
                  />
                </div>
                <div className="max-w-sm">
                  <h3 className="text-2xl font-bold text-gray-50 sm:text-[32px]">
                    {feature.title}
                  </h3>
                  <p className="mt-8 text-sm font-light text-gray-300 sm:text-base ">
                    {feature.description}
                  </p>
                </div>
              </div>
              <div className="mt-8 h-[238px] w-[334px] sm:h-[448px] sm:w-[600px] md:mt-0 lg:h-[409px] lg:w-[576px]">
                <Image
                  src={feature.dashboard}
                  alt={`${feature.title} Dashboard`}
                  width={0}
                  height={0}
                  sizes="210rem"
                  style={{
                    objectFit: "contain",
                    width: "100%",
                    height: "100%",
                  }}
                  loading="lazy"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LearningRevolution;
