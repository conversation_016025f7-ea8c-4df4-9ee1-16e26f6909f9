{"extends": ["next", "next/core-web-vitals", "eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "project": ["./tsconfig.json"]}, "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "curly": ["error", "multi"], "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/consistent-type-definitions": ["error", "type"], "react/react-in-jsx-scope": "off", "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/consistent-type-exports": "error"}, "env": {"browser": true, "es2021": true}}