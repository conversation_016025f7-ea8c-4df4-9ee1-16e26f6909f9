import React from "react";
import Image from "next/image";

const AchieveMore = () => {
  const features = [
    {
      title: "Smart Learning Suggestions",
      description:
        "Access curated videos, documents, and links tailored to your needs, helping you master every topic for your entry test with ease.",
      image: "/assets/icons/learning_suggestions.png",
      dashboard: "/assets/images/learning_suggestions.png",
      isReversed: false,
    },
    {
      title: "Interactive MCQ Solver",
      description:
        "Solve tricky MCQs with step by step explanations and expert tips for better conceptual understanding.",
      image: "/assets/icons/mcq_solver.png",
      dashboard: "/assets/images/mcq_solver.png",
      isReversed: true,
    },
  ];

  return (
    <div className="mx-auto flex w-full max-w-7xl flex-col items-center px-4 sm:px-12 sm:py-16 md:px-16">
      <div className="space-y-10 text-center">
        <h2 className="text-3xl font-semibold sm:text-4xl md:text-5xl">
          Achieve More, Smarter
        </h2>
        <p className=" text-sm font-normal text-gray-600 sm:text-lg md:px-48">
          From personalized tests to real-time progress tracking, see how
          Parhlai empowers you to ace your entry exams.
        </p>
      </div>

      <div className="mt-28 w-full  md:space-y-32">
        {features.map((feature, index) => (
          <div
            key={index}
            className={`flex flex-col items-center justify-between lg:flex-row lg:items-start ${
              feature.isReversed ? "lg:flex-row-reverse" : ""
            }`}
          >
            <div
              className={`mx-8 mt-12 flex w-full flex-col items-center space-y-6 text-center ${
                feature.isReversed
                  ? "md:pl-8 lg:items-end lg:text-right"
                  : "md:pr-8 lg:items-start lg:text-left"
              } lg:w-1/3`}
            >
              <div className="mb-4 h-20 w-20">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  width={80}
                  height={80}
                  style={{ objectFit: "contain" }}
                  loading="lazy"
                />
              </div>
              <div className="max-w-sm">
                <h3 className="text-2xl font-bold sm:text-[32px]">
                  {feature.title}
                </h3>
                <p className="mt-8 text-sm font-light text-gray-600 sm:text-base ">
                  {feature.description}
                </p>
              </div>
            </div>
            <div className="mt-8 h-[238px] w-[334px] sm:h-[428px] sm:w-[600px] md:mt-0 lg:h-[409px] lg:w-[576px]">
              <Image
                src={feature.dashboard}
                alt={`${feature.title} Dashboard`}
                width={0}
                height={0}
                sizes="210rem"
                style={{ objectFit: "contain", width: "100%", height: "100%" }}
                loading="lazy"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AchieveMore;
