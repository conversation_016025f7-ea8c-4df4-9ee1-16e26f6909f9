import React from "react";
import FeatureCard from "./FeatureCard";
import NewsLetterCard from "./NewsLetterCard";

const Features = () => {
  return (
    <div className="flex w-full flex-col gap-y-16 px-6 md:gap-y-16">
      <div className="w-full">
        <div className="justify-left flex sm:pl-[8%]">
          <h1 className="text-2xl font-extrabold sm:text-3xl md:text-5xl">
            Accelerate your Entry-Test <br />
            Preparation with Parhlai!
          </h1>
        </div>
      </div>
      <div className="grid grid-flow-row grid-rows-4 gap-8 sm:px-[4%] md:px-[8%] lg:flex lg:min-w-full lg:flex-row lg:flex-wrap">
        {[
          {
            title: "Unlimited MCQ Generation",
            description:
              "Never run out of challenging questions with our AI-powered Multiple Choice Questions (MCQs).",
            imageUrl: "/assets/images/robot.png",
            backgroundColor: "bg-tertiary",
          },
          {
            title: "Personalized Tests",
            description:
              "Customize your exams based on your strengths and areas prone to improvement.",
            imageUrl: "/assets/images/textbook.png",
            backgroundColor: "bg-primary",
          },
          {
            title: "Insightful AI Analytics",
            description:
              "Get valuable insights into your performance with our AI-based analytics.",
            imageUrl: "/assets/images/business.png",
            backgroundColor: "bg-primary",
          },
          {
            title: "Affordable Pricing",
            description:
              "Access top-notch resources starting as low as 100rs. No long-term commitments, just pay for what you need.",
            imageUrl: "/assets/images/money.png",
            backgroundColor: "bg-tertiary",
          },
        ].map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            imageUrl={feature.imageUrl}
            backgroundColor={feature.backgroundColor}
          />
        ))}
      </div>
      <div className="px-[0%] md:px-[8%]">
        <NewsLetterCard />
      </div>
    </div>
  );
};

export default Features;
