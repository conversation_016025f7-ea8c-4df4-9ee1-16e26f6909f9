import { type AxiosError } from "axios";
import { 
  type Blog,
  type BlogsResponse, 
  type SingleBlogResponse,
  type BlogsQueryParams,
  type CreateBlogData,
  type UpdateBlogData,
  type BlogImageUploadResponse
} from "@/types/blog";
import { type APIResponse } from "@/types/common";
import { api } from ".";

/**
 * Get all blogs with optional query parameters
 */
export const getBlogs = async (params?: BlogsQueryParams) => {
  try {
    const response = await api.get<APIResponse<BlogsResponse>>("/blogs/", {
      params,
    });
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<BlogsResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: [],
        pagination: {
          totalItems: 0,
          totalPages: 0,
          currentPage: 1,
          pageSize: 10,
          hasNext: false,
          hasPrev: false,
        },
      }
    );
  }
};

/**
 * Get a single blog by ID
 */
export const getSingleBlog = async (id: string) => {
  try {
    const response = await api.get<APIResponse<SingleBlogResponse>>(`/blogs/${id}`);
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<SingleBlogResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: null,
      }
    );
  }
};

/**
 * Create a new blog (requires authentication)
 */
export const createBlog = async (blogData: CreateBlogData) => {
  try {
    const response = await api.post<APIResponse<SingleBlogResponse>>("/blogs/", blogData);
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<SingleBlogResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: null,
      }
    );
  }
};

/**
 * Update an existing blog (requires authentication)
 */
export const updateBlog = async (id: string, blogData: UpdateBlogData) => {
  try {
    const response = await api.put<APIResponse<SingleBlogResponse>>(`/blogs/${id}`, blogData);
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<SingleBlogResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: null,
      }
    );
  }
};

/**
 * Delete a blog (requires authentication)
 */
export const deleteBlog = async (id: string) => {
  try {
    const response = await api.delete<APIResponse<{ message: string }>>(`/blogs/${id}`);
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<{ message: string }>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
      }
    );
  }
};

/**
 * Get upload URL for blog images (requires authentication)
 */
export const getBlogImageUploadURL = async (fileName: string, fileType: string) => {
  try {
    const response = await api.post<APIResponse<BlogImageUploadResponse>>("/blogs/upload-url", {
      fileName,
      fileType,
    });
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<BlogImageUploadResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        uploadUrl: "",
        imageUrl: "",
        key: "",
      }
    );
  }
};
