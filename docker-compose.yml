# docker-compose version
version: "3.8"

# docker-compose services
services:
  # service name anything we want we called it web
  frontend-parhlai:
    # path to dockerfile is called build
    build:
      context: .
      dockerfile: Dockerfile

    # restart: always

    # image name we called it nextjs-cicd-docker:version
    # image: frontend-exapro:1.0

    # ports to expose first 3000 is the port of the container and the second 3000 is the port we define in the dockerfile
    ports:
      - "3000:3000"
