"use client";
import React from "react";
import { loginSchema } from "@/schemas/auth.schema";
import type * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
const Page = () => {
  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const isLoading = form.formState.isSubmitting;

  const handleSubmit = async (values: z.infer<typeof loginSchema>) => {
    console.log(values);
  };

  return (
    <div className="flex h-[500px] w-[500px] flex-col items-center justify-center space-y-4  shadow-xl">
      <h1 className="text-2xl font-bold">Login</h1>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="flex flex-col items-center justify-center space-y-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs uppercase dark:text-primary ">
                  Email
                </FormLabel>
                <FormControl>
                  <Input
                    disabled={isLoading}
                    className="border-0 bg-zinc-200/60 ring-offset-0 focus-visible:ring-0 dark:bg-input"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs uppercase dark:text-primary ">
                  password
                </FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    disabled={isLoading}
                    className="border-0 bg-zinc-200/60  ring-offset-0 
                                    focus-visible:ring-0 dark:bg-input"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <Button
            variant="default"
            size="lg"
            className=" mt-2 rounded-[3px] bg-black px-4 py-0.5 text-white"
            type="submit"
          >
            {isLoading ? (
              <div className="flex gap-2">Loading...</div>
            ) : (
              "Log In"
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default Page;
