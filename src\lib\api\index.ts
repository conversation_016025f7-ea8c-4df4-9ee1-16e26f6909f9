import { config } from "@/lib/config";
import axios from "axios";
import {
  errorInterceptor,
  requestInterceptor,
  successInterceptor,
} from "./interceptors";

export const api = axios.create({
  baseURL: config.apiBaseUrl, //API in Server
  headers: {
    "Content-type": "application/json",
  },
});

api.interceptors.request.use(requestInterceptor);
api.interceptors.response.use(successInterceptor, errorInterceptor);
