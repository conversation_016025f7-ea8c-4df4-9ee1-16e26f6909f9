import React from "react";
import TestimonialCard from "./TestimonialCard";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { getRedirectUrl } from "@/lib/utils";
import { Button } from "@/components/ui/button";

// Testimonial data
const testimonials = [
  {
    content:
      "Great idea for the current college student. Pehle launch hota to mujhe bhi faida hojata 😌",
    author: "<PERSON>",
    avatar: "/assets/images/testimonial1.jpg",
    subtitle: "Public Ad. student @ NUST",
  },
  {
    content:
      "Agar yeh platform pehle hota, to coaching walon pe itne paisay na lagane parte. Jaldi launch karo yaar! 🚀",
    author: "Asghar",
    avatar: "/assets/images/Asghar.png",
    subtitle: "CS student @ NUST",
  },
  {
    content:
      "Har saal itne students entry test ki tension leke ghoomte hain, <PERSON><PERSON><PERSON><PERSON> ho to self-study bhi asaan ho jaye gi 🥳",
    author: "Abdullah",
    avatar: "/assets/images/testimonial2.jpg",
    subtitle: "CS student @ COMSATS",
  },
];
const TestimonialsAndTeam = ({ env }: { env?: string }) => {
  return (
    <div
      id="testimonial"
      className="mx-auto w-full max-w-7xl pt-16 sm:px-6 lg:px-8"
    >
      <div className="mb-24 flex flex-col items-center gap-y-12">
        <div className="mb-16 space-y-8 px-4 text-center md:mb-28 md:space-y-10">
          <h2 className="px-3 text-3xl font-semibold text-gray-700 sm:text-5xl">
            What people say about us
          </h2>
          <p className="mt-4 text-sm font-normal text-gray-500 md:px-36 md:text-lg">
            Parhlai aims to simplify entry test preparation, making self-study
            more effective and reducing the need for expensive coaching. Here is
            what students think!
          </p>
        </div>

        <div className="block sm:hidden">
          <div className="relative">
            <Carousel className="mx-auto w-full max-w-[17rem] shadow-[2px_0px_0px_rgba(0,0,0,0.1),-2px_0px_0px_rgba(0,0,0,0.1)]">
              <CarouselContent>
                {testimonials.map((testimonial, index) => (
                  <CarouselItem key={index}>
                    <Card className="border-none shadow-none">
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          {/* Avatar and Author Info */}
                          <div className="flex items-center gap-3">
                            <Avatar className="h-12 w-12">
                              <AvatarImage
                                src={testimonial.avatar}
                                alt={testimonial.author}
                              />
                              <AvatarFallback>
                                {testimonial.author[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div className="text-left">
                              <h4 className="font-semibold text-gray-900">
                                {testimonial.author}
                              </h4>
                              <p className="text-sm text-gray-500">
                                {testimonial.subtitle}
                              </p>
                            </div>
                          </div>

                          <p className="text-left leading-relaxed text-gray-600">
                            {testimonial.content}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="-left-10 h-8 w-8 border-2 border-gray-200 bg-white shadow-md hover:border-gray-300 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-30" />
              <CarouselNext className="-right-10 h-8 w-8 border-2 border-gray-200 bg-white shadow-md hover:border-gray-300 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-30" />
            </Carousel>
          </div>
        </div>

        <div className="masonry sm:masonry-sm md:masonry-md hidden sm:block">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              content={testimonial.content}
              author={testimonial.author}
              subtitle={testimonial.subtitle}
              avatar={testimonial.avatar}
            />
          ))}
        </div>
        <Link href={getRedirectUrl("register", env)}>
          <Button size={"cta"}>Register Now</Button>
        </Link>
      </div>
    </div>
  );
};

export default TestimonialsAndTeam;
