import type {
  AxiosRequestHeaders,
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

export type ConsoleError = {
  status: number;
  data: unknown;
};

export const requestInterceptor = async (
  config: InternalAxiosRequestConfig,
): Promise<InternalAxiosRequestConfig> => {
  if (config.data && (config.method === "post" || config.method === "put")) {
    const hashHex = await getPayloadHash(config.data);
    config.headers = {
      ...(config.headers || {}),
      "Content-Type": "application/json",
      "x-amz-content-sha256": hashHex,
    } as unknown as AxiosRequestHeaders;
  } else
    config.headers = {
      ...(config.headers || {}),
    } as unknown as AxiosRequestHeaders;

  return config;
};

export const successInterceptor = (response: AxiosResponse): AxiosResponse => {
  return response;
};

export const errorInterceptor = async (error: AxiosError): Promise<void> => {
  if (error.response?.status === 401) await Promise.reject(error);
  else {
    if (error.response) {
      const errorMessage: ConsoleError = {
        status: error.response.status,
        data: error.response.data,
      };
      console.error(errorMessage);
    } else if (error.request) console.error(error.request);
    else console.error("Error", error?.message);
    await Promise.reject(error);
  }
};

export const getPayloadHash = async (payload: unknown) => {
  const body = JSON.stringify(payload);
  const encoder = new TextEncoder();
  const data = encoder.encode(body);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray
    .map((byte) => byte.toString(16).padStart(2, "0"))
    .join("");
  return hashHex;
};
