import Link from "next/link";
import React from "react";

const TermsContent = () => {
  const termsAndConditionsContent = [
    {
      heading: "Acceptance of Terms",
      content:
        "You must read, agree with, and accept all of the terms and conditions contained in these Terms before using the Services. We may amend these Terms at any time by posting the amended terms on the Parhlai website (the 'Site'). All amended terms shall be effective immediately upon posting. We will also notify you of any upcoming changes to these Terms. Your continued use of the Services after any such changes constitutes your acceptance of the amended Terms.",
    },
    {
      heading: "License",
      content:
        "In consideration of your payment of the appropriate subscription fee for the Services you subscribe to, and your agreement to and compliance with these Terms, we grant you a limited, non-exclusive, non-transferable license to use the Services.",
    },
    {
      heading: "Refunds",
      content:
        "Unless otherwise provided in these Terms or at our sole discretion, no refunds, cancellations, or changes to subscriptions will be allowed.",
    },
    {
      heading: "Account Security",
      content:
        "You must complete the Parhlai registration process to subscribe to and use the Services by providing accurate and up-to-date information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to notify Parhlai immediately of any unauthorized use of your account or any other breach of security.",
    },
    {
      heading: "Ownership",
      content:
        "The Services, including all intellectual property rights therein, are the sole and exclusive property of Parhlai. Your subscription grants you the right to use the Services in accordance with these Terms, but you do not become the owner of the Services or any associated intellectual property rights.",
    },
    {
      heading: "Subscription Term",
      content:
        "Your subscription entitles you to use the Services for the subscription period specified in your subscription package. Your subscription will automatically renew unless you cancel it before the renewal date. Subscriptions cannot be suspended or reactivated later, and no extensions are offered for unused periods.",
    },
    {
      heading: "Prohibitions",
      content:
        "You are expressly prohibited from copying, reverse engineering, or modifying the Services. You may not sublicense, assign, share, sell, rent, lease, or otherwise transfer your right to use the Services. Unauthorized use of the Services may result in termination of your subscription without refund.",
    },
    {
      heading: "Limitation of Liability",
      content:
        "Parhlai provides the Services 'as is' and without any warranty or condition, express, implied, or statutory. We specifically disclaim any liability for indirect, incidental, consequential, or special damages arising out of or in connection with your use of the Services. Our maximum liability to you will not exceed your subscription fee.",
    },
    {
      heading: "Confidentiality",
      content:
        "We will maintain the confidentiality of all user communications containing personal information transmitted directly to Parhlai, except as required by law. Postings on public forums are not confidential and may be used and disclosed by Parhlai as deemed appropriate.",
    },
    {
      heading: "DMCA Guidelines",
      content:
        "Parhlai complies with the Digital Millennium Copyright Act (DMCA). If you believe that your copyrighted material has been infringed, please notify <NAME_EMAIL> with the required information.",
    },
    {
      heading: "Miscellaneous",
      content:
        "These Terms constitute the entire agreement between you and Parhlai regarding the Services and supersede all prior agreements, representations, and undertakings. These Terms shall be governed by and construed in accordance with the laws of [jurisdiction], without regard to its conflict of law provisions.",
    },
  ];

  return (
    <div className="flex h-fit w-[92%] flex-col rounded-[16px] bg-white px-11 py-8 drop-shadow-xl">
      <h3 className=" mb-2 text-3xl font-extrabold">Terms of Service</h3>
      <p className="font-base mb-10 text-gray-400">
        Last updated 2nd May, 2024
      </p>
      <p className="leading-10">
        Welcome to{" "}
        <Link
          className="text-base font-bold underline"
          target="_blank"
          href={"https://parhlai.com"}
        >
          Parhlai!
        </Link>{" "}
        These terms of service (&quot;Terms&quot;) constitute a legally binding
        agreement between you and{" "}
        <Link
          className="text-base underline"
          target="_blank"
          href={"https://parhlai.com"}
        >
          Parhlai
        </Link>{" "}
        (&quot;we,&quot; &quot;us,&quot; or &quot;our&quot;) governing your use
        of the{" "}
        <Link
          className="text-base underline"
          target="_blank"
          href={"https://parhlai.com"}
        >
          Parhlai
        </Link>{" "}
        website and services (collectively, the &quot;Services&quot;). By
        accessing or using the Services, you agree to be bound by these Terms.
        If you do not agree to these Terms, please do not use the Services.
      </p>
      {termsAndConditionsContent.map((section, index) => (
        <div key={index} className="">
          <h4 className="text-lg font-semibold leading-10">
            {section.heading}
          </h4>
          <p className=" leading-10">{section.content}</p>
        </div>
      ))}
    </div>
  );
};

export default TermsContent;
