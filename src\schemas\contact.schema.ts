import { z } from "zod";
export const contactSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required.")
    .min(4, "Name must be at least 4 characters.")
    .max(24, "Maximum length of Name is 24 characters."),
  email: z
    .string()
    .min(1, "Email Address is required.")
    .email("Invalid Email Address."),
  entryTest: z.string().min(1, "Entry Test is required."),
  university: z.string().min(1, "University name is required."),
  message: z.string().min(1, "Message is required."),
});
