import Description from "@/components/landing/home/<USER>";
import TailorMadeFeatures from "@/components/landing/home/<USER>";
import AchieveMore from "@/components/landing/home/<USER>";
import LearningRevolution from "@/components/landing/home/<USER>";
import TestimonialsAndTeam from "@/components/landing/home/<USER>";
import PricingSection from "@/components/landing/home/<USER>";
import FAQSection from "@/components/landing/home/<USER>";
import NewsletterSection from "@/components/landing/home/<USER>";

const Home = () => {
  const env = process.env.APP_ENV;
  return (
    <main className="mt-32 flex h-full flex-col items-center space-y-24 sm:mt-36 lg:mt-0">
      <Description env={env} />
      <TailorMadeFeatures env={env} />
      <AchieveMore />
      <LearningRevolution env={env} />
      <TestimonialsAndTeam env={env} />
      <PricingSection env={env} />
      <FAQSection />
      <NewsletterSection />
    </main>
  );
};
export default Home;
