import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none disabled:cursor-not-allowed",
  {
    variants: {
      variant: {
        black: "bg-black text-primary-foreground hover:bg-slate-700",
        white: "bg-white text-black hover:bg-slate-100",
        default: "bg-primary text-primary-foreground text-center hover:bg-primary-300 ",
        destructive:
          "bg-cta text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary-900 underline-offset-4 hover:underline px-0 text-base",

        icon: "bg-none"
      },
      size: {
        default: "h-10 px-4 py-2",
        login:"h-[46px] px-10 p-0 w-[128px]",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-16 text-base rounded-md px-10 w-64 rounded-[10px] font-semibold",
        cta: "lg:h-16 h-11 lg:w-64 lg:rounded-[10px] lg:text-base lg:px-10 px-8 lg:font-semibold",
        icon: "h-10 w-10",
        download: "h-[55px] w-[190px] sm:h-[60px] sm:w-[225px] md:h-[66px] rounded-[10px] md:w-[264px] border"
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
