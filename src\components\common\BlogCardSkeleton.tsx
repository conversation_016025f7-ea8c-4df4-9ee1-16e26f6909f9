import React from "react";

type BlogCardSkeletonProps = {
  isLarge?: boolean;
  isMedium?: boolean;
  isSmallMedium?: boolean;
};

const BlogCardSkeleton = ({
  isLarge = false,
  isMedium = false,
  isSmallMedium = false,
}: BlogCardSkeletonProps) => {
  if (isLarge)
    // Large card skeleton - same height as medium but wider (spans 2 columns)
    return (
      <div className="h-full animate-pulse">
        <div className="h-full overflow-hidden rounded-[10px] bg-gray-200 shadow-lg">
          <div className="h-56 bg-gray-300"></div>
          <div className="flex flex-col px-6 pb-7 pt-5">
            <div className="mb-2 h-6 rounded bg-gray-300"></div>
            <div className="mb-1 h-6 w-3/4 rounded bg-gray-300"></div>
            <div className="mb-4 space-y-2">
              <div className="h-4 rounded bg-gray-300"></div>
              <div className="h-4 rounded bg-gray-300"></div>
              <div className="h-4 w-5/6 rounded bg-gray-300"></div>
              <div className="h-4 w-2/3 rounded bg-gray-300"></div>
            </div>
            {/* Tags skeleton */}
            <div className="mt-auto flex flex-wrap gap-2">
              <div className="h-6 w-16 rounded-[20px] bg-gray-300"></div>
              <div className="h-6 w-20 rounded-[20px] bg-gray-300"></div>
            </div>
          </div>
        </div>
      </div>
    );

  if (isMedium)
    // Medium card skeleton - slightly bigger than normal
    return (
      <div className="h-full animate-pulse">
        <div className="h-full overflow-hidden rounded-[10px] bg-gray-200 shadow-lg">
          <div className="h-56 bg-gray-300"></div>
          <div className="flex flex-col px-6 pb-7 pt-5">
            {/* Tags skeleton at top */}
            <div className="mb-3 flex flex-wrap gap-2">
              <div className="h-6 w-16 rounded-[20px] bg-gray-300"></div>
              <div className="h-6 w-20 rounded-[20px] bg-gray-300"></div>
            </div>
            <div className="mb-2 h-6 rounded bg-gray-300"></div>
            <div className="mb-1 h-6 w-3/4 rounded bg-gray-300"></div>
            <div className="space-y-2">
              <div className="h-4 rounded bg-gray-300"></div>
              <div className="h-4 rounded bg-gray-300"></div>
              <div className="h-4 w-5/6 rounded bg-gray-300"></div>
              <div className="h-4 w-2/3 rounded bg-gray-300"></div>
            </div>
          </div>
        </div>
      </div>
    );

  if (isSmallMedium)
    // Small-medium card skeleton - between normal and medium
    return (
      <div className="h-full animate-pulse">
        <div className="h-full overflow-hidden rounded-[10px] bg-gray-200 shadow-lg">
          <div className="h-52 bg-gray-300"></div>
          <div className="flex flex-col px-5 pb-6 pt-4">
            {/* Tags skeleton at top */}
            <div className="mb-3 flex flex-wrap gap-2">
              <div className="h-5 w-14 rounded-[20px] bg-gray-300"></div>
              <div className="w-18 h-5 rounded-[20px] bg-gray-300"></div>
            </div>
            <div className="mb-2 h-5 rounded bg-gray-300"></div>
            <div className="mb-1 h-5 w-3/4 rounded bg-gray-300"></div>
            <div className="space-y-2">
              <div className="h-3 rounded bg-gray-300"></div>
              <div className="h-3 rounded bg-gray-300"></div>
              <div className="h-3 w-5/6 rounded bg-gray-300"></div>
            </div>
          </div>
        </div>
      </div>
    );

  // Normal card skeleton
  return (
    <div className="h-full animate-pulse">
      <div className="h-full overflow-hidden rounded-[10px] bg-gray-200 shadow-lg">
        <div className="h-48 bg-gray-300"></div>
        <div className="flex flex-col p-4">
          {/* Tags skeleton at top */}
          <div className="mb-3 flex flex-wrap gap-2">
            <div className="h-6 w-16 rounded-full bg-gray-300"></div>
            <div className="h-6 w-20 rounded-full bg-gray-300"></div>
          </div>
          <div className="mb-3 h-5 rounded bg-gray-300"></div>
          <div className="mb-1 h-5 w-3/4 rounded bg-gray-300"></div>
          <div className="mb-4 space-y-2">
            <div className="h-4 rounded bg-gray-300"></div>
            <div className="h-4 rounded bg-gray-300"></div>
            <div className="h-4 w-5/6 rounded bg-gray-300"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCardSkeleton;
