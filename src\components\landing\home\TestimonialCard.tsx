import React from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";

type TestimonialCardProps = {
  content: string;
  subtitle: string;
  author: string;
  avatar: string;
};

const TestimonialCard = ({
  content,
  author,
  avatar,
  subtitle,
}: TestimonialCardProps) => {
  return (
    <Card className={"break-inside mb-4 rounded-lg bg-white p-2 shadow"}>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex gap-x-2">
            <div className="col-span-1 row-span-1">
              <Avatar className="h-14 w-14">
                <AvatarImage src={avatar} alt={author} />
                <AvatarFallback>{author[0]}</AvatarFallback>
              </Avatar>
            </div>
            {/* <span className="col-span-5 px-4 font-medium text-gray-900">
            {author}
          </span> */}
            <div className="col-span-5 w-full text-left ">
              <h4 className="font-semibold text-gray-900">{author}</h4>
              <p className="text-sm text-slate-500">{subtitle}</p>
            </div>
          </div>
          <p className="col-span-6 text-base text-gray-600">{content}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TestimonialCard;
