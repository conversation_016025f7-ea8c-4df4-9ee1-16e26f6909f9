import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQSection = () => {
  const faqData = [
    {
      question: "What is Parhlai?",
      answer:
        "Parhlai is an AI-driven platform designed to help students in Pakistan prepare for university entry tests with customized resources and cutting-edge features.",
    },
    {
      question: "How does the unlimited MCQ generation feature work?",
      answer:
        "Our AI generates an unlimited pool of multiple-choice questions based on the specific test syllabus, ensuring you never run out of practice material.",
    },
    {
      question: "Can I create personalized tests?",
      answer:
        "Yes, Parhlai allows you to customize tests according to your strengths and areas where you need improvement, helping you focus on your weak points.",
    },
    {
      question: "What kind of analytics does <PERSON><PERSON><PERSON><PERSON> provide?",
      answer:
        "<PERSON><PERSON>hl<PERSON> offers insightful analytics that track your performance, highlight trends, and provide actionable recommendations for better preparation.",
    },
    {
      question: "How much does Parhlai cost?",
      answer:
        "Parhlai offers affordable pricing, starting as low as 100 PKR. You only pay for the resources you need, with no long-term commitments.",
    },
    {
      question: "Is there a subscription plan for <PERSON><PERSON><PERSON><PERSON>?",
      answer:
        "Parhlai offers a flexible pay-as-you-go model. However, you can subscribe to our newsletter to stay updated on new features and offerings.",
    },
    {
      question:
        "How can I ensure that the questions align with the test syllabus?",
      answer:
        "Our AI is trained to follow the guidelines and syllabus of university entry tests in Pakistan, ensuring all questions are relevant and up to date.",
    },
  ];

  return (
    <div className="w-full px-4 sm:px-6 lg:px-8">
      <div className="mx-auto my-12 max-w-4xl">
        <div className="mb-16 space-y-8 text-center md:mb-28 md:space-y-10">
          <h2 className="text-3xl font-semibold text-gray-700 sm:text-5xl">
            Frequently Asked Questions
          </h2>
          <p className="px-2 text-sm text-gray-500 sm:text-lg md:px-8">
            Find out how Parhlai makes entry test preparation easier, faster,
            and more effective. Here are some common questions students ask
            about our platform and features.
          </p>
        </div>

        <Accordion type="single" collapsible className="w-full space-y-6">
          {faqData.map((faq, index) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="rounded-[15px] px-4 shadow-[2px_0px_0px_rgba(0,0,0,0.1),0px_2px_0px_rgba(0,0,0,0.1)]"
            >
              <AccordionTrigger className="text-left text-sm font-semibold text-gray-900 hover:no-underline sm:text-lg [&[data-state=open]>div>svg]:rotate-180">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-sm font-medium text-gray-600 sm:text-base">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
};

export default FAQSection;
