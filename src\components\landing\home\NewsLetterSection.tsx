"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useModal } from "@/hooks/use-modal-store";
import LoadingSpinner from "../../common/LoadingSpinner";
import { subscribeNewsletter } from "@/lib/api/newsletter";

const NewsletterSection = () => {
  const { onOpen } = useModal();
  const [email, setEmail] = useState<string>("");
  const [isValid, setIsValid] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const onEmailChange = (email: string) => {
    if (email === "") setIsValid(true);
    setEmail(email);
    const re =
      // eslint-disable-next-line
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (re.test(email)) setIsValid(true);
    else setIsValid(false);
  };

  const callSubscribe = async (email: string) => {
    setLoading(true);
    const response = await subscribeNewsletter(email);
    console.log(response);

    onOpen("newsletterSubscription", {
      newsletterStatus: response?.status,
    });
    setLoading(false);
  };

  return (
    <div className="relative w-full bg-gradient-to-r from-primary to-customPurple px-4 pt-16 lg:py-16">
      <div className="mt-28 lg:ml-16">
        <div className="mt-12 flex flex-col items-center justify-between gap-12 lg:flex-row">
          <div className="flex w-full flex-col space-y-8 px-4 text-center lg:mb-12 lg:w-1/2 lg:text-left">
            <div className="space-y-4">
              <h2 className="mb-12 text-3xl font-bold leading-tight text-white sm:text-4xl md:text-5xl">
                The journey to your dream university starts today!
              </h2>
              <p className="pr-4 text-base text-gray-200">
                Join Parhlai newsletter list today, to stay updated with the
                latest updates. You can unsubscribe anytime.
              </p>
            </div>

            <div className="grid grid-cols-1 items-center justify-stretch gap-x-2 gap-y-2 sm:px-4 md:flex md:grid-cols-2 lg:px-0 lg:pr-20 2xl:w-9/12">
              <Input
                type="email"
                placeholder="Email"
                onChange={(e) => onEmailChange(e.target.value)}
                className="col-span-3 col-start-1  h-14 w-full bg-white focus-visible:ring-0 focus-visible:ring-offset-0 md:shrink lg:grow"
              />
              <Button
                disabled={email ? !isValid : true}
                variant={
                  email ? (!isValid ? "destructive" : "default") : "default"
                }
                className={cn(
                  "col-span-1 col-start-1 m-0 h-14 w-44 justify-self-center text-black hover:text-white md:col-start-4 md:shrink",
                  !email && !isValid && "bg-white opacity-[100%]",
                )}
                onClick={() => callSubscribe(email)}
              >
                {loading ? (
                  <>
                    <LoadingSpinner
                      width={20}
                      height={20}
                      className="mx-[2px]"
                    />{" "}
                    <div>Sending...</div>
                  </>
                ) : (
                  "Subscribe"
                )}
              </Button>
            </div>
          </div>

          <div className="hidden md:absolute md:bottom-0 md:right-0 lg:block">
            <Image
              src="/assets/images/newsletter_dash.png"
              alt="Dashboard Preview"
              width={596}
              height={430}
              sizes="210rem"
              style={{ objectFit: "contain" }}
              loading="lazy"
            />
          </div>
          <div className="lg:hidden ">
            <Image
              src="/assets/images/mobile_dash.png"
              alt="Dashboard Preview"
              width={596}
              height={430}
              sizes="210rem"
              style={{ objectFit: "contain" }}
              loading="lazy"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewsletterSection;
