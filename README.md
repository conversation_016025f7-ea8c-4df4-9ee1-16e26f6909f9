# frontend-landing

Frontend repository for Parhlai Landing. Built on Next Js, with typescript, shadCN UI and tailwindcss.

Uses: Node 20.11.1 and Yarn 4.1.1

## Run Locally

### Clone the project

```bash
  https://github.com/Parhlai/frontend-parhlai.git
```

(Right now, the repository is private. So you will have to use a personal access token to authenticate while cloning the repository. Personal access token will be given as the password. Refer https://github.com/settings/tokens )
(Or, just setup SSH keys : https://medium.com/@kyledeguzmanx/quick-step-by-step-guide-to-generating-an-ssh-key-in-github-d3c6f7e185bb )

### Go to the project directory

```bash
  cd frontend-parhlai
```

### Install dependencies

```bash
  yarn install
```

### Sample `.env` file

```Dotenv
  AWS_COGNITO_REGION=
  AWS_COGNITO_POOL_ID=
  AWS_COGNITO_APP_CLIENT_ID=
  PROD_API_URL=
```

### Prettier Setup

Project uses prettier as code formatter. Therefore it is advised to <strong>install</strong> prettier vscode extension, <strong>configure</strong> prettier as default formatter for .ts files, and <strong>enable</strong> format on save in vscode settings. Follow these steps for complete walkthrough:

- Install prettier extension from vscode marketplace
- Open any .ts file in the project.
- press <kbd>ctrl</kbd> + <kbd>shift</kbd> + <kbd>P</kbd> to open command pallete.
- Search "Format Document With" and choose the option.
- Select "Configure default formmatter"
- Select "Prettier" from the list of formatters.

And you are done! All formatting will be applied when you save any file.

Alternatively, you can run command `yarn prettier:check` to check any formatting issues and `yarn prettier` to format all files at once in the project.

#### ATTENTION!

It is highly advised to run the above command before commiting your code, in order to rectify any mis-formatting. (Once CI is designed, this need for checking formatting will no longer be needed).

## Project run

### Start the server

```bash
  yarn dev
```

View on http://localhost:3000/
