import Image from "next/image";
import { buttonVariants } from "@/components/ui/button";
import Link from "next/link";
import React from "react";

const HomeFooter = () => {
  return (
    <div className="flex flex-col items-center space-y-16 px-[10%]">
      <div className="flex w-full flex-col gap-y-8  lg:flex-row lg:gap-x-24">
        <div className="flex w-full flex-col gap-y-8 lg:w-1/2">
          <div className="w-3/5 sm:w-2/5 lg:w-3/5 xl:w-2/5">
            <Image
              src="/assets/icons/parhlai_expanded.svg"
              alt="icon"
              width={0}
              height={0}
              style={{ objectFit: "contain", width: "100%", height: "auto" }}
            />
          </div>

          <p className="font-regular text-base">
            Parhlai is your AI-guided solution for mastering university entry
            tests in Pakistan. Prepare with confidence, ensuring your success
            with our cutting-edge platform tailored to your needs.
          </p>
          <div className="footer-icons flex flex-col">
            <p className="mb-3 text-xl font-semibold">Follow Us</p>
            <div className="flex items-center">
              {[
                {
                  icon: "facebook",
                  url: "https://www.facebook.com/people/Parhlai/61558369453378/",
                },
                { icon: "instagram", url: "https://www.instagram.com/parhlai" },
                {
                  icon: "linkedin",
                  url: "https://www.linkedin.com/company/parhlai",
                },
                // { icon: "twitter", url: "https://twitter.com/example" },
              ].map((platform, index) => (
                <div key={index} className="mr-2">
                  <a
                    href={platform.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={`/assets/icons/${platform.icon}.svg`}
                      alt={platform.icon}
                      width={50}
                      height={50}
                    />
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-y-20 lg:w-1/2 lg:flex-row lg:justify-center lg:gap-x-16">
          {[
            {
              title: "Quick Links",
              links: [
                { title: "Home", url: "/" },
                { title: "Product", url: "#learning-revolution" },
                { title: "Blog", url: "/blog" },
                { title: "About us", url: "#testimonial" },
              ],
            },
            {
              title: "Support",
              links: [
                { title: "Contact Us", url: "/contact" },
                { title: "Report a Bug", url: "/" },
                { title: "Request Feature", url: "/" },
              ],
            },
            {
              title: "Legal",
              links: [
                { title: "Privacy Policy", url: "/privacy-policy" },
                { title: "Terms & Conditions", url: "/terms-of-service" },
              ],
            },
          ].map((section, index) => (
            <div key={index} className="flex flex-col gap-y-6 lg:gap-y-8">
              <p className="whitespace-nowrap text-xl font-semibold">
                {section.title}
              </p>
              {section.links.map((link, index) => (
                <Link
                  key={index}
                  className={(buttonVariants({ variant: "link" }), "h-[19px]")}
                  href={link.url}
                >
                  {link.title}
                </Link>
              ))}
            </div>
          ))}
        </div>
      </div>
      <div className="flex h-[56px] w-full flex-col items-center justify-center">
        <p className="text-center text-[#68769F]">
          © 2025, Parhlai. All rights reserved.
        </p>
      </div>
    </div>
  );
};

export default HomeFooter;
