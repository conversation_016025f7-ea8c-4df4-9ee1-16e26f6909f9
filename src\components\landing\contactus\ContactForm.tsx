"use client";
import React, { useState } from "react";
import type * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { contactSchema } from "@/schemas/contact.schema";
import LoadingSpinner from "@/components/common/LoadingSpinner";
import { cn } from "@/lib/utils";
import { Textarea } from "@/components/ui/textarea";
import { universities, entryTests } from "@/lib/constants/contact-us";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { sendEmail } from "@/lib/api/contact";
import { ScrollArea } from "@/components/ui/scroll-area";

const ContactForm = () => {
  const [error, setError] = useState<string>("");
  const form = useForm<z.infer<typeof contactSchema>>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      entryTest: "",
      university: "",
      message: "",
    },
  });
  const isLoading = form.formState.isSubmitting;

  const onSubmit = async (values: z.infer<typeof contactSchema>) => {
    const response = await sendEmail(values);
    console.log(response);
    setError("");
  };
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-3/4 grid-cols-1 gap-x-24 gap-y-8 md:grid-cols-2 2xl:w-3/5"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="col-span-1 row-start-1">
              <FormLabel
                className={cn(
                  "text-xs dark:text-primary ",
                  error && "text-destructive dark:text-destructive",
                )}
              >
                Name
                <span className="normal-case italic text-destructive dark:text-destructive">
                  {error ? " - " + error : " *"}
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  required
                  disabled={isLoading}
                  className=" rounded-[6px] border-slate-300 bg-white ring-offset-0 focus-visible:ring-0"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="col-span-1 row-start-2 md:row-start-1">
              <FormLabel
                className={cn(
                  "text-xs  dark:text-primary ",
                  error && "text-destructive dark:text-destructive",
                )}
              >
                Email
                <span className="normal-case italic text-destructive dark:text-destructive">
                  {error ? " - " + error : " *"}
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  required
                  disabled={isLoading}
                  className=" rounded-[6px] border-slate-300 bg-white ring-offset-0 focus-visible:ring-0"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="entryTest"
          render={({ field }) => (
            <FormItem className="col-span-1 row-start-3 flex flex-col md:row-start-2">
              <FormLabel
                className={cn(
                  "text-xs  dark:text-primary ",
                  error && "text-destructive dark:text-destructive",
                )}
              >
                Entry Test
                <span className="normal-case italic text-destructive dark:text-destructive">
                  {error ? " - " + error : " *"}
                </span>
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "justify-between overflow-hidden rounded-[6px] border-slate-300 bg-white ring-offset-0 hover:bg-white focus-visible:ring-0",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value
                        ? entryTests.find((test) => test.value === field.value)
                            ?.label
                        : "I'm preparing for"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="p-0">
                  <Command>
                    <CommandInput placeholder="Search language..." />
                    <CommandEmpty>No language found.</CommandEmpty>
                    <CommandGroup>
                      {entryTests.map((test) => (
                        <CommandItem
                          className="aria-selected:bg-slate-100"
                          value={test.label}
                          key={test.value}
                          onSelect={() => {
                            form.setValue("entryTest", test.value);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              test.value === field.value
                                ? "opacity-100"
                                : "opacity-0",
                            )}
                          />
                          {test.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="university"
          render={({ field }) => (
            <FormItem className="col-span-1 row-start-4 flex flex-col md:row-start-2 ">
              <FormLabel
                className={cn(
                  "text-xs  dark:text-primary ",
                  error && "text-destructive dark:text-destructive",
                )}
              >
                University
                <span className="normal-case italic text-destructive dark:text-destructive">
                  {error ? " - " + error : " *"}
                </span>
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      className={cn(
                        "justify-between  rounded-[6px] border-slate-300 bg-white ring-offset-0 hover:bg-white  focus-visible:ring-0",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      <p className="overflow-hidden">
                        {field.value
                          ? universities.find(
                              (uni) => uni.value === field.value,
                            )?.label
                          : "I want to get in"}
                      </p>

                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="h-[400px] p-0 ">
                  <Command>
                    <CommandInput placeholder="Search language..." />
                    <CommandEmpty>No language found.</CommandEmpty>
                    <ScrollArea className="h-[400px] overflow-auto">
                      <CommandGroup>
                        {universities.map((uni) => (
                          <CommandItem
                            className="aria-selected:bg-slate-100"
                            value={uni.label}
                            key={uni.value}
                            onSelect={() => {
                              form.setValue("university", uni.value);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                uni.value === field.value
                                  ? "opacity-100"
                                  : "opacity-0",
                              )}
                            />
                            {uni.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </ScrollArea>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem className="md:col-span-2 ">
              <FormLabel
                className={cn(
                  "text-xs  dark:text-primary ",
                  error && "text-destructive dark:text-destructive",
                )}
              >
                Message
                <span className="normal-case italic text-destructive dark:text-destructive">
                  {error ? " - " + error : " *"}
                </span>
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Type your message here"
                  className="h-40 resize-none bg-white placeholder:text-slate-400"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button
          variant="black"
          size="lg"
          className="bg-slate-900 md:col-span-2 md:mx-[25%]"
          type="submit"
        >
          {isLoading ? (
            <div className="flex gap-2">
              <LoadingSpinner /> Loading...
            </div>
          ) : (
            "Send Message"
          )}
        </Button>
      </form>
    </Form>
  );
};

export default ContactForm;
