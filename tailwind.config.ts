import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    fontWeight: {
      thin: "100",
      hairline: "100",
      extralight: "200",
      light: "300",
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
      extrabold: "900",
    },
    extend: {
      colors: {
        customPurple: "#5936CDB3",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          100: "#5031B9",
          200: "#472BA4",
          300: "#3E2690",
          400: "#2D1B67",
          500: "#241652",
          600: "#1B103D",
          700: "#120B29",
          800: "#090514",
          900: "#000000",
          foreground: "hsl(var(--primary-foreground))",
        },
        purple: "#3f3cbb",
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        // accent: {
        //   DEFAULT: "hsl(var(--accent))",
        //   foreground: "hsl(var(--accent-foreground))",
        // },
        accent: {
          DEFAULT: "#5936CD",
          0: "#FFFFFF",
          0.5: "#EEEBFA",
          1: "#DED7F5",
          1.5: "#CDC3F0",
          2: "#BDAFEB",
          3: "#9B86E1",
          4: "#7A5ED7",
          5: "#5936CD",
          6: "#472BA4",
          7: "#35207B",
          8: "#241652",
          8.5: "#1B103E",
          9: "#120B29",
          9.5: "#090515",
          10: "#000000",
        },
        dark: "",
        tertiary: {
          DEFAULT: "hsl(var(--tertiary))",
        },
        success: "hsl(var(--success))",
        cta: "hsl(var(--cta))",
        gray: { DEFAULT: "hsl(var(--gray))", 500: "#1E1E1E" },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
