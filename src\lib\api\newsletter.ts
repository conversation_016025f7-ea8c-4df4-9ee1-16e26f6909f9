import { type AxiosError } from "axios";
import { type NewsLetterResponse } from "@/types/newsletter";
import { type APIResponse } from "@/types/common";
import { api } from ".";

export const subscribeNewsletter = async (email: string) => {
  try {
    const response = await api.post<APIResponse<NewsLetterResponse>>(
      "/newsletter/subscribe/",
      { email },
    );
    console.log(response.data);

    return response.data;
  } catch (error: unknown) {
    return (
      (error as AxiosError<APIResponse<NewsLetterResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
      }
    );
  }
};
