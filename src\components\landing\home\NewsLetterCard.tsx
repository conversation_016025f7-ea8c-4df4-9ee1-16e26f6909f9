"use client";
import Image from "next/image";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "../../ui/button";
import { subscribeNewsletter } from "@/lib/api/newsletter";
import { cn } from "@/lib/utils";
import { useModal } from "@/hooks/use-modal-store";
import LoadingSpinner from "../../common/LoadingSpinner";

const NewsLetterCard = () => {
  const { onOpen } = useModal();
  const [email, setEmail] = useState<string>("");
  const [isValid, setIsValid] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const onEmailChange = (email: string) => {
    if (email === "") setIsValid(true);
    setEmail(email);
    // don't remember from where i copied this code, but this works.
    const re =
      // eslint-disable-next-line
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (re.test(email)) setIsValid(true);
    else setIsValid(false);
  };

  const callSubscribe = async (email: string) => {
    setLoading(true);
    const response = await subscribeNewsletter(email);
    console.log(response);

    onOpen("newsletterSubscription", {
      newsletterStatus: response?.status,
    });
    setLoading(false);
  };
  return (
    <div className="flex w-full items-center justify-center  from-primary to-accent-1 md:h-[423px] md:w-full md:justify-between md:rounded-[32px] md:bg-gradient-to-r md:px-[8%] lg:px-[10%]">
      <div className="hidden md:block">
        <Image
          src="/assets/images/coming-soon.png"
          alt="newsletter"
          width={300}
          height={300}
          style={{ width: "100%", height: "auto" }}
          sizes="210rem"
        />
      </div>

      <div className=" flex flex-col items-center space-y-6 md:max-w-[65%] md:items-start md:p-[6%] lg:max-w-[45%] lg:p-[0%]">
        <h2 className=" text-2xl font-bold md:text-3xl">
          The journey to your dream university starts today!
        </h2>
        <p className="font-regular text-center text-base md:text-left">
          Join the Parhlai! newsletter list today, to stay updated with the
          latest updates, you can unsubscribe anytime.
        </p>
        <div className="grid grid-cols-2 items-center justify-stretch gap-x-2 gap-y-1 md:flex 2xl:w-9/12">
          <Input
            type="email"
            placeholder="Email"
            onChange={(e) => onEmailChange(e.target.value)}
            className="col-span-3 col-start-1 h-10 w-full bg-white focus-visible:ring-0 focus-visible:ring-offset-0 md:shrink lg:grow"
          />
          <Button
            disabled={email ? !isValid : true}
            variant={email ? (!isValid ? "destructive" : "black") : "black"}
            className={cn(
              "col-span-1 col-start-4 flex justify-between md:shrink",
              !email && !isValid && "opacity-[50%]",
            )}
            onClick={() => callSubscribe(email)}
          >
            {loading ? (
              <>
                <LoadingSpinner width={20} height={20} className="mx-[2px]" />{" "}
                <div>Sending...</div>
              </>
            ) : (
              "Subscribe"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewsLetterCard;
