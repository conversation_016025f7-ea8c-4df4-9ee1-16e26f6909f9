import { type AxiosError } from "axios";
import { type ContactEmailResponse, type TContactForm } from "@/types/contact";
import { type APIResponse } from "@/types/common";
import { api } from ".";

export const sendEmail = async (contactForm: TContactForm) => {
  try {
    const response = await api.post<APIResponse<ContactEmailResponse>>(
      "/contact/mail/",
      contactForm,
    );
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<ContactEmailResponse>>).response
        ?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
      }
    );
  }
};
