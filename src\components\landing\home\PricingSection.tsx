import React from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { getRedirectUrl } from "@/lib/utils";
import Link from "next/link";

const PricingSection = ({ env }: { env?: string }) => {
  return (
    <div className="relative w-full overflow-hidden bg-gradient-to-br from-primary to-customPurple px-4 py-16 sm:px-6 lg:px-8">
      <div className="mx-auto mb-8 mt-12 max-w-7xl">
        <div className="m-8 mb-24 flex flex-col justify-between text-center text-white md:flex-row md:text-left">
          <div className="flex flex-col items-start">
            <h2 className="text-3xl font-semibold sm:text-5xl ">
              Affordable Pricing, better results!
            </h2>
            <div className="mt-4 space-y-4 text-center text-sm  font-normal text-gray-200 sm:text-lg md:text-left">
              <p className="mt-4">Get Started with Parhlai today!</p>
              <p className="mt-2">
                Preparing for entry tests has never been easier. Now available
                across all devices!
              </p>
            </div>
          </div>
          <div className="mt-8 text-black ">
            <Link href={getRedirectUrl("register", env)}>
              <Button variant={"white"} size={"cta"}>
                Register Now
              </Button>
            </Link>
          </div>
        </div>

        <div className="relative mx-auto max-w-6xl">
          <div className="flex  flex-row items-center justify-center md:items-end ">
            <div className="mb-10 sm:mb-14 md:ml-8 ">
              <Image
                src="/assets/images/mobile.png"
                alt="Parhlai Mobile App Interface"
                width={286}
                height={537}
                sizes="210rem"
                loading="lazy"
              />
            </div>
            <div>
              <Image
                src="/assets/images/pricing_dashboard.png"
                alt="Parhlai Desktop Interface"
                width={669}
                height={476}
                sizes="210rem"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingSection;
