import React from "react";
import SingleBlogPost from "@/components/landing/blog/SingleBlogPost";
import { BlogProvider } from "@/contexts/BlogContext";

// Configure Edge Runtime for Cloudflare Pages
export const runtime = "edge";

type BlogPostPageProps = {
  params: {
    slug: string;
  };
};

const BlogPostPage = ({ params }: BlogPostPageProps) => {
  return (
    <BlogProvider>
      <SingleBlogPost slug={params.slug} />
    </BlogProvider>
  );
};

export default BlogPostPage;
