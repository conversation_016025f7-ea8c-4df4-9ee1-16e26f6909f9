import Image from "next/image";
import React from "react";

type BannerProps = {
  label: string;
  iconUrl: string;
};

const LabelBanner = ({ label, iconUrl }: BannerProps) => {
  return (
    <div className="relative flex w-full flex-col bg-gradient-to-r from-primary to-[#DED7F5]">
      <div className="z-10 flex h-64 flex-wrap items-center justify-center overflow-hidden sm:h-[315px]">
        {Array.from({ length: 200 }, (_, i) => (
          <Image
            className="z-0 mx-3 my-4 h-8 w-8"
            key={i}
            src={iconUrl} // Assuming the images have different names
            alt="telephone"
            width={32}
            height={32}
            loading="lazy"
          />
        ))}
      </div>
      <div className="absolute bottom-[50%] z-10 w-full translate-y-1/2">
        <div className="h-10 w-full sm:h-16">
          <Image
            src="/assets/icons/parhlai_expanded_white_trans.svg"
            alt="icon"
            width={0}
            height={0}
            style={{ objectFit: "contain", width: "100%", height: "100%" }}
          />
        </div>
        <h1
          className="text-center text-[34px] font-extrabold tracking-tight
        text-white sm:max-md:text-5xl md:text-7xl"
        >
          {label}
        </h1>
      </div>
    </div>
  );
};

export default LabelBanner;
