"use client";
import { buttonVariants } from "@/components/ui/button";
import { cn, getRedirectUrl } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import NavigationSheet from "./NavigationSheet";

const HomeNav = ({ env }: { env?: string }) => {
  const pathname = usePathname();
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isScrollUp, setIsScrollUp] = useState(false);
  const navItems = [
    { label: "Home", href: "/", type: "scroll" },
    {
      label: "Product",
      href: pathname === "/" ? "#learning-revolution" : "/#learning-revolution",
      type: "scroll",
    },
    // {
    //   label: "Pricing",
    //   href: pathname === "/" ? "#learning-revolution" : "/#learning-revolution",
    // },
    { label: "Blog", href: "/blog", type: "link" },
    { label: "Contact us", href: "/contact", type: "link" },
    {
      label: "Testimonials",
      href: pathname === "/" ? "#testimonial" : "/#testimonial",
      type: "scroll",
    },
  ];

  // Helper function to perform smooth scroll
  const smoothScrollToElement = (targetId: string) => {
    const element = document.getElementById(targetId);
    if (element) {
      // Calculate the offset to account for navigation height
      // Desktop nav is h-24 (96px) + buffer for proper spacing
      const navHeight = 96; // Desktop nav height
      const elementPosition =
        element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navHeight;

      // Scroll to the calculated position
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  // Helper function to extract ID from href
  const extractTargetId = (href: string): string => {
    // Handle both "#section" and "/#section" formats
    return href.startsWith("/#") ? href.slice(2) : href.slice(1);
  };

  // Scroll detection for sticky header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const navHeight = 120; // Height of the nav + some buffer (96px + 24px buffer)
      const scrollThreshold = 0; // Reduced threshold for more responsive detection

      // Calculate scroll direction
      const scrollDifference = currentScrollY - lastScrollY;
      const isScrollingUp = scrollDifference < 0;
      const isScrollingDown = scrollDifference > 0;

      // Determine if we're past the initial nav height (should be in sticky zone)
      const isInStickyZone = currentScrollY > navHeight;

      // Update scroll direction state
      if (Math.abs(scrollDifference) > scrollThreshold)
        if (isScrollingUp && !isScrollUp) setIsScrollUp(true);
        else if (isScrollingDown && isScrollUp) setIsScrollUp(false);

      // Update visibility state
      if (isInStickyZone) {
        // In sticky zone - show header only when scrolling up
        if (!isVisible) setIsVisible(true);
      } else if (isVisible) {
        // Above sticky zone - hide header and reset scroll up state
        setIsVisible(false);
        setIsScrollUp(false);
      }

      setLastScrollY(currentScrollY);
    };

    // Add scroll listener with throttling
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandleScroll, { passive: true });

    // Cleanup
    return () => window.removeEventListener("scroll", throttledHandleScroll);
  }, [lastScrollY, isScrollUp, isVisible]);

  const navigationClickHandler = (
    event: React.MouseEvent<HTMLAnchorElement>,
    item: (typeof navItems)[0],
  ) => {
    if (item.type === "scroll") {
      event.preventDefault();
      const targetId = extractTargetId(item.href);

      if (pathname !== "/") {
        // Navigate to home page first, then scroll
        router.push("/");

        // Use a single reliable method for post-navigation scrolling
        let hasScrolled = false;

        const attemptScroll = () => {
          if (hasScrolled) return; // Prevent double scrolling

          const element = document.getElementById(targetId);
          if (element) {
            smoothScrollToElement(targetId);
            hasScrolled = true;
          } else setTimeout(attemptScroll, 50); // Retry if element not found yet (max 10 attempts)
        };

        // Start attempting to scroll after navigation
        setTimeout(attemptScroll, 100);
      } else smoothScrollToElement(targetId); // Already on home page, scroll immediately
    }
  };

  return (
    <>
      <div
        className={cn(
          "flex h-20 w-full flex-row-reverse items-center justify-between bg-background px-[7.5%] py-3.5 md:h-24 lg:w-[85%] lg:flex-row lg:justify-around lg:px-0",
          // Mobile: always fixed
          "fixed top-0 z-50",
          // Large screens: static positioning (not sticky)
          "lg:static lg:z-auto",
        )}
      >
        <div className="size-6  lg:hidden" />
        <div className="flex h-full justify-start">
          <Image
            src="/assets/icons/parhlai_white_purple.svg"
            alt="icon"
            width={0}
            height={0}
            style={{ objectFit: "contain", width: "auto", height: "100%" }}
          />
        </div>
        <div className="ml-9 hidden w-1/2 grow justify-around lg:flex xl:justify-start xl:space-x-4">
          {navItems?.map((item, index) => (
            <Link
              key={index}
              className={buttonVariants({ variant: "link" })}
              href={item.href}
              onClick={(event) => navigationClickHandler(event, item)}
            >
              {item.label}
            </Link>
          ))}
        </div>
        <div className="hidden h-full flex-auto items-center justify-end space-x-8 lg:flex">
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "",
            )}
            href={getRedirectUrl("register", env)}
          >
            Register
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "bg-accent-3",
            )}
            href={getRedirectUrl("login", env)}
          >
            Sign in
          </Link>
        </div>
        <NavigationSheet env={env} />
      </div>
      <div
        className={cn(
          "fixed top-0 z-50 h-24 w-full flex-row items-center justify-around bg-background/90 px-[7.5%] py-3.5 shadow-lg backdrop-blur-md",
          // Mobile: hidden
          "hidden",
          // Large screens: conditional sticky behavior
          "lg:flex",
          // Animation classes
          "transition-all duration-300 ease-out",
          // Transform animations - show when visible and scrolling up, hide when scrolling down
          isVisible && isScrollUp ? "translate-y-0" : "-translate-y-full",
        )}
      >
        <div className="size-6  lg:hidden" />
        <div className="flex h-full justify-start">
          <Image
            src="/assets/icons/parhlai_white_purple.svg"
            alt="icon"
            width={0}
            height={0}
            style={{ objectFit: "contain", width: "auto", height: "100%" }}
          />
        </div>
        <div className="ml-9 hidden w-1/2 grow justify-around lg:flex xl:justify-start xl:space-x-4">
          {navItems?.map((item, index) => (
            <Link
              key={index}
              className={buttonVariants({ variant: "link" })}
              href={item.href}
              onClick={(event) => navigationClickHandler(event, item)}
            >
              {item.label}
            </Link>
          ))}
        </div>
        <div className="hidden h-full flex-auto items-center justify-end space-x-8 lg:flex">
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "",
            )}
            href={getRedirectUrl("register", env)}
          >
            Register
          </Link>
          <Link
            className={cn(
              buttonVariants({ variant: "default", size: "login" }),
              "bg-accent-3",
            )}
            href={getRedirectUrl("login", env)}
          >
            Sign in
          </Link>
        </div>
        <NavigationSheet env={env} />
      </div>
    </>
  );
};

export default HomeNav;
