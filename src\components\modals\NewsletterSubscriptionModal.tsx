import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { useModal } from "@/hooks/use-modal-store";
import Image from "next/image";
import { Button } from "../ui/button";

const NewsletterSubscriptionModal = () => {
  const { isOpen, onClose, type, data } = useModal();
  const { newsletterStatus } = data;
  const isModalOpen = isOpen && type === "newsletterSubscription";
  return (
    <Dialog open={isModalOpen} onOpenChange={onClose}>
      <DialogContent className="bg-white">
        <DialogHeader className="flex w-full items-center pt-10">
          <div className="relative my-4 flex h-[60px] w-[72px] items-center">
            <Image
              fill
              src={
                newsletterStatus === 201
                  ? "/assets/images/envelope.png"
                  : newsletterStatus === 400
                    ? "/assets/images/email_failed.png"
                    : "/assets/images/email_404.png"
              }
              alt={
                newsletterStatus === 201
                  ? "success email"
                  : newsletterStatus === 400
                    ? "failed email"
                    : "404 email"
              }
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          <DialogTitle className="text-lg font-bold">
            {newsletterStatus === 201
              ? "Thank you for subscribing!"
              : newsletterStatus === 400
                ? "Email already subscribed!"
                : "Something went wrong"}
          </DialogTitle>
          <DialogDescription className="text-sm">
            {newsletterStatus === 201
              ? "You will now receive timely updates from the team at Parhlai! You can unsubscribe from the mailing list at any time."
              : newsletterStatus === 400
                ? "The email you entered has already been subscribed to the Parhlai! mailing list. Try again with another email."
                : "Unfortunately, your system encountered a internal service error. Check your internet connection & try again."}
          </DialogDescription>
        </DialogHeader>
        <DialogClose asChild>
          <Button variant={"destructive"} onClick={onClose}>
            {newsletterStatus === 201
              ? "Close"
              : newsletterStatus === 400
                ? "Input another email"
                : "Retry"}
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default NewsletterSubscriptionModal;
